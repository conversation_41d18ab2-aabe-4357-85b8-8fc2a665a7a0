import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:injectable/injectable.dart' as i;
import 'package:v_card/app/controllers/appointment_controller.dart';
import 'package:v_card/app/controllers/business_card_controller.dart';
import 'package:v_card/app/controllers/dashboard_controller.dart';
import 'package:v_card/app/controllers/enquiries_controller.dart';
import 'package:v_card/app/controllers/v_card_controller.dart';
import 'package:v_card/app/data/apis/repository/auth_repo.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';
import 'package:v_card/app/services/auth_service.dart';

@i.lazySingleton
@i.injectable
class AuthController extends GetxController {
  final AuthRepository authRepository;
  AuthController({required this.authRepository}) {
    onInit();
  }

  // Platform check
  bool get isAndroid => !kIsWeb && Platform.isAndroid;

  // --------------------------- Register Section --------------------------- //

  final registerfirstNameController = TextEditingController();
  final registerlastNameController = TextEditingController();
  final registeremailController = TextEditingController();
  final registerContactController = TextEditingController();
  final registerCountryCode = '91'.obs;
  final registerCountryflag = '🇮🇳'.obs;
  final registerpasswordController = TextEditingController();
  final registerConfirmPasswordController = TextEditingController();
  final registerpassObscure = true.obs;
  final registerconfirmPassObscure = true.obs;
  final registerLoading = false.obs;
  final registerState = ApiState.initial().obs;

  // --------------------------- Login Section --------------------------- //

  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  final passObscure = true.obs;
  final signInState = ApiState.initial().obs;
  final isLoadingLogin = false.obs;
  final isLoadingGoogleLogin = false.obs;
  final errorText = ''.obs;
  final googleErrorText = ''.obs;

  // --------------------------- Forgot Password Section --------------------------- //

  final forgotPasswordEmailController = TextEditingController();
  final forgotPasswordState = ApiState.initial().obs;
  final isLoadingForgotPassword = false.obs;

  // --------------------------- Reset Password Section --------------------------- //

  final resetNewPasswordController = TextEditingController();
  final resetConfirmPasswordController = TextEditingController();
  final resetPassObscure = true.obs;
  final resetConfirmPassObscure = true.obs;

  // --------------------------- Change Password Section --------------------------- //

  final changeOldPasswordController = TextEditingController();
  final changeNewPasswordController = TextEditingController();
  final changeConfPasswordController = TextEditingController();
  final changeOldPassObscure = true.obs;
  final changeNewPassObscure = true.obs;
  final changeNewConfPassObscure = true.obs;
  final changePasswordState = ApiState.initial().obs;
  final changepasswordLoading = false.obs;

  // --------------------------- Logout --------------------------- //
  final isLoadingLogout = false.obs;
  final isLoadingDeleteAcount = false.obs;

  // --------------------------- Auth Functions --------------------------- //

  Future<void> signInWithGoogle() async {
    isLoadingGoogleLogin.value = true;
    try {
      final GoogleUser? googleUser =
          await AuthService.instance.signInWithGoogle();
      if (googleUser != null) {
        final response = await authRepository.googleLoginApi(
          email: googleUser.email,
          name: googleUser.displayName ?? '',
        );

        if (response.success == true) {
          getIt<SharedPreferences>().setEmail = googleUser.email;
          getIt<SharedPreferences>().setToken = response.data?.token.toString();
          getIt<SharedPreferences>().setRole = response.data?.role.toString();
          getIt<SharedPreferences>().setUserId =
              response.data?.userId.toString();
          getIt<SharedPreferences>().setLoggedIn = true;

          NavigationService.navigateWithSlideAnimation(
            AppRoutes.nevigationMenu,
          );
          clearTextControllers();
        } else {
          googleErrorText.value = response.message;
        }
      } else {
        googleErrorText.value = "Google Sign-In failed. Please try again.";
      }
    } catch (e) {
      Logger.log(e.toString());
      googleErrorText.value = "Google Sign-In failed. Please try again.";
    } finally {
      isLoadingGoogleLogin.value = false;
    }
  }

  // Future<void> signInWithGoogle() async {
  //   isLoadingGoogleLogin.value = true;
  //   try {
  //     final String? idToken = await AuthService.instance.signInWithGoogle();
  //     if (idToken != null) {
  //       final response = await authRepository.googleLoginApi(token: idToken);

  //       getIt<SharedPreferences>().setToken = response.data?.token.toString();
  //       getIt<SharedPreferences>().setRole = response.data?.role.toString();
  //       getIt<SharedPreferences>().setUserId = response.data?.userId.toString();
  //       getIt<SharedPreferences>().setLoggedIn = true;
  //       if (response.success == true) {
  //         NavigationService.navigateWithSlideAnimation(
  //           AppRoutes.nevigationMenu,
  //         );
  //         clearTextControllers();
  //       } else {
  //         googleErrorText.value = response.message;
  //       }
  //     } else {
  //       googleErrorText.value = "Google Sign-In failed. Please try again.";
  //     }
  //   } catch (e) {
  //     Logger.log(e.toString());
  //     googleErrorText.value = "Google Sign-In failed. Please try again.";
  //   } finally {
  //     isLoadingGoogleLogin.value = false;
  //   }
  // }

  Future<void> signIn() async {
    isLoadingLogin.value = true;
    signInState.value = LoadingState();

    try {
      final response = await authRepository.loginApi(
        email: emailController.text.trim(),
        password: passwordController.text.trim(),
      );

      getIt<SharedPreferences>().setEmail = emailController.text.trim();
      getIt<SharedPreferences>().setToken = response.data?.token.toString();
      getIt<SharedPreferences>().setRole = response.data?.role.toString();
      getIt<SharedPreferences>().setUserId = response.data?.userId.toString();
      getIt<SharedPreferences>().setLoggedIn = true;

      if (response.success == true) {
        NavigationService.navigateWithSlideAnimation(AppRoutes.nevigationMenu);
        clearTextControllers();
      } else {
        errorText.value = response.message;
      }
    } catch (e) {
      Logger.log(e.toString());
    } finally {
      isLoadingLogin.value = false;
    }
  }

  Future<void> signUp() async {
    registerLoading.value = true;
    registerState.value = LoadingState();

    try {
      final response = await authRepository.registerApi(
        firstName: registerfirstNameController.text.trim(),
        lastName: registerlastNameController.text.trim(),
        email: registeremailController.text.trim(),
        password: registerpasswordController.text.trim(),
        contact: registerContactController.text.trim(),
        regionCode: registerCountryCode.trim(),
        termPolicyCheck: "true",
      );

      if (response.success == true) {
        registerState.value = SuccessState(response);
        NavigationService.navigateWithSlideAnimation(AppRoutes.login);
        clearTextControllers();
      }
    } catch (e) {
      Logger.log(e.toString());
    } finally {
      registerLoading.value = false;
    }
  }

  Future<void> forgotPassword(FormState? currentState) async {
    isLoadingForgotPassword.value = true;
    forgotPasswordState.value = LoadingState();

    try {
      final response = await authRepository.forgotPasswordApi(
        email: forgotPasswordEmailController.text.trim(),
        urlDomain: isAndroid ? 'http:' : 'myapp:',
      );

      if (response.success == true) {
        forgotPasswordState.value = SuccessState(response);
        clearTextControllers();
        isLoadingForgotPassword.value = false;
      }
    } catch (e) {
      Logger.log(e.toString());
    } finally {
      isLoadingForgotPassword.value = false;
    }
  }

  Future<void> resetPassword(FormState? currentState) async {
    resetConfirmPassObscure.value = true;
    signInState.value = LoadingState();
    final email = getIt<SharedPreferences>().getEmail;

    try {
      final response = await authRepository.resetPasswordApi(
        token: '',
        email: email!,
        password: resetNewPasswordController.text.trim(),
        confirmPassword: resetConfirmPasswordController.text.trim(),
      );

      if (response.success == true) {
        signInState.value = SuccessState(response);
        clearTextControllers();
        NavigationService.navigateWithSlideAnimation(AppRoutes.login);
      }
    } catch (e) {
      Logger.log(e.toString());
    } finally {
      resetConfirmPassObscure.value = false;
    }
  }

  Future<void> changePassword(FormState? currentState) async {
    changepasswordLoading.value = true;
    if (currentState == null || !currentState.validate()) return;

    try {
      final email = getIt<SharedPreferences>().getEmail;
      final response = await authRepository.changePasswordApi(
        email: email!,
        oldPassword: changeOldPasswordController.text.trim(),
        newPassword: changeNewPasswordController.text.trim(),
        confirmPassword: changeConfPasswordController.text.trim(),
      );

      if (response.success == true) {
        changePasswordState.value = SuccessState(response);
        clearTextControllers();
        NavigationService.navigateWithSlideAnimation(AppRoutes.nevigationMenu);
      }
    } catch (e) {
      Logger.log(e.toString());
    } finally {
      changepasswordLoading.value = false;
    }
  }

  Future<void> logout() async {
    try {
      final prefs = getIt<SharedPreferences>();
      isLoadingLogout.value = true;
      final email = getIt<SharedPreferences>().getEmail;
      final token = getIt<SharedPreferences>().getToken;
      final response = await authRepository.logoutApi(
        email: email!,
        authToken: token!,
      );

      if (response.success == true) {
        AuthService.instance.signOutGoogle();
        // dashboardController.reset();

        getIt<DashboardController>().reset();
        getIt<VcardController>().reset();
        getIt<BusinesscardController>().reset();
        getIt<EnquiriesController>().reset();
        getIt<AppointmentController>().reset();

        Get.deleteAll(force: true);

        clearTextControllers();
        prefs.clear();

        prefs.setOnboardingCompleted = true;
        NavigationService.navigateWithSlideAnimation(AppRoutes.login);
      } else {
        errorText.value = response.message;
        isLoadingLogin.value = false;
      }
    } catch (e) {
      Logger.log(e.toString());
    } finally {
      isLoadingLogout.value = false;
    }
  }

  Future<void> deleteAccount() async {
    isLoadingDeleteAcount.value = true;
    final id = getIt<SharedPreferences>().getUserId;

    try {
      await authRepository.deleteAccountApi(id: id!);
      getIt<SharedPreferences>().clear();
      NavigationService.navigateWithSlideAnimation(AppRoutes.login);
    } catch (e) {
      Logger.log(e.toString());
    } finally {
      isLoadingDeleteAcount.value = false;
    }
  }

  // --------------------------- Utility Functions --------------------------- //

  void clearTextControllers() {
    registerfirstNameController.clear();
    registerlastNameController.clear();
    registeremailController.clear();
    registerpasswordController.clear();
    registerConfirmPasswordController.clear();

    emailController.clear();
    passwordController.clear();

    forgotPasswordEmailController.clear();

    resetNewPasswordController.clear();
    resetConfirmPasswordController.clear();

    changeOldPasswordController.clear();
    changeNewPasswordController.clear();
    changeConfPasswordController.clear();
  }

  @override
  @i.disposeMethod
  void dispose() {
    // Dispose all controllers
    registerfirstNameController.dispose();
    registerlastNameController.dispose();
    registeremailController.dispose();
    registerpasswordController.dispose();
    registerConfirmPasswordController.dispose();

    emailController.dispose();
    passwordController.dispose();

    forgotPasswordEmailController.dispose();

    resetNewPasswordController.dispose();
    resetConfirmPasswordController.dispose();

    changeOldPasswordController.dispose();
    changeNewPasswordController.dispose();
    changeConfPasswordController.dispose();

    super.dispose();
  }
}
