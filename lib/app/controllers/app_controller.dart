import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:injectable/injectable.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

@lazySingleton
class AppController extends GetxController {
  // Tracks whether assets have been precached
  final Completer<bool> _assetsCached = Completer<bool>();

  // Tracks whether the splash sequence has completed
  final RxBool splashCompleted = false.obs;

  // Tracks the current locale
  final Rx<Locale> currentLocale =
      Locale(getIt<SharedPreferences>().getAppLocal ?? 'en').obs;

  // Handle the splash screen flow
  Future<void> onSplash(BuildContext context) async {
    // If splash is already completed, go directly to destination
    if (splashCompleted.value) {
      _navigateBasedOnAuthStatus();
      return;
    }

    // Start precaching assets immediately
    _preCacheAssets(context)
        .then((_) {
          if (!_assetsCached.isCompleted) {
            _assetsCached.complete(true);
          }
        })
        .catchError((error) {
          if (!_assetsCached.isCompleted) {
            // Even on error, allow app to continue
            _assetsCached.complete(true);
            Logger.log("Asset precaching error: $error");
          }
        });

    // Wait for splash duration
    Future.delayed(const Duration(seconds: 3), () async {
      try {
        // Wait for assets to be cached
        final assetsReady = await _assetsCached.future;
        if (assetsReady) {
          _navigateBasedOnAuthStatus();
          splashCompleted.value = true;
        }
      } catch (e) {
        // On error, proceed anyway
        _navigateBasedOnAuthStatus();
        splashCompleted.value = true;
        Logger.log("Splash error: $e");
      }
    });
  }

  // Handle navigation based on auth status
  void _navigateBasedOnAuthStatus() {
    final isLoggedIn = getIt<SharedPreferences>().getLoggedIn;
    final onboardingCompleted =
        getIt<SharedPreferences>().getOnboardingCompleted;

    if (isLoggedIn) {
      NavigationService.navigateWithSlideAnimation(AppRoutes.nevigationMenu);
    } else if (!onboardingCompleted) {
      NavigationService.navigateWithSlideAnimation(AppRoutes.welcomePage);
    } else {
      NavigationService.navigateWithSlideAnimation(AppRoutes.login);
    }
  }

  // Update the app's locale
  void updateLocale(String languageCode) {
    currentLocale.value = Locale(languageCode);
    Get.updateLocale(Locale(languageCode));
  }

  // Precache assets for better performance
  Future<void> _preCacheAssets(BuildContext context) async {
    try {
      final manifest = await AssetManifest.loadFromAssetBundle(rootBundle);
      final assets = manifest.listAssets();

      if (assets.isEmpty) {
        return; // No assets to precache
      }

      final listOfPng = assets
          .where((element) => element.endsWith('.png'))
          .map((e) => precacheImage(AssetImage(e), context, onError: _onError));

      final listOfSvg = assets
          .where((element) => element.endsWith('.svg'))
          .map(SvgAssetLoader.new);

      // Wait for all assets to be precached
      await Future.wait([
        ...listOfPng,
        ...listOfSvg.map((e) => e.loadBytes(context)),
      ]);

      // Cache SVG keys if context is still mounted
      if (context.mounted) {
        for (final e in listOfSvg) {
          e.cacheKey(context);
        }
      }
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('Error in _preCacheAssets: $e\n$stackTrace');
      }
    }
  }

  // Error handler for precaching
  void _onError(Object exception, StackTrace? stackTrace) {
    if (kDebugMode) {
      exception.logWithName('precacheImageError');
    }
  }
}
