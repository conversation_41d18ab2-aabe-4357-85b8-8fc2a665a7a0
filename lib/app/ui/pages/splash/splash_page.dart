import 'package:get/get.dart';
import 'package:v_card/app/controllers/app_controller.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';
import 'package:v_card/app/utils/helpers/generated/assets.gen.dart';

class SplashPage extends GetItHook<AppController> {
  const SplashPage({super.key});

  @override
  Widget build(BuildContext context) {
    return CustomImageView(
      height: double.infinity,
      width: double.infinity,
      imagePath: Assets.images.png.other.pngFullSplash.path,
    );
    // return AnnotatedRegion<SystemUiOverlayStyle>(
    //   value: SystemUiOverlayStyle(
    //     statusBarColor: Colors.transparent,
    //     statusBarIconBrightness: Brightness.light,
    //     systemNavigationBarIconBrightness: Brightness.light,
    //     systemNavigationBarColor: Theme.of(context).customColors.primaryColor,
    //   ),
    //   child: BackgroundImage(
    //     imagePath: Assets.images.png.other.pngFullSplash.path,
    //     child: AnimatedSplashScreen(
    //       duration: 6000,
    //       splashIconSize: 120.0,
    //       splash: CustomImageView(imagePath: AssetConstants.pngSplash),
    //       backgroundColor: Colors.transparent,
    //       nextScreen: DashboardPage(),
    //       splashTransition: SplashTransition.fadeTransition,
    //     ),
    //   ),
    // );
  }

  @override
  bool get canDisposeController => true;

  @override
  void onDispose() {}

  @override
  void onInit() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.onSplash(Get.context ?? Get.context!);
    });
  }
}
