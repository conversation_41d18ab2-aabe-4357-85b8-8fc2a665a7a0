import 'package:get/get.dart';
import 'package:v_card/app/controllers/business_card_controller.dart';
import 'package:v_card/app/data/model/business/business_card_model.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class BusinessCardPage extends GetItHook<BusinesscardController> {
  const BusinessCardPage({super.key});

  @override
  void onInit() {
    controller.checkNetworkAndLoad();
    // getIt<VcardController>().getOnlyVcardList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Get.theme.customColors.white,
      appBar: _buildAppBar(),
      floatingActionButton: _buildScannerFAB(),
      body: _buildBody(context),
    );
  }

  CustomAppbar _buildAppBar() {
    return CustomAppbar(
      title: AppText(
        AppStrings.T.lbl_business_card,
        style: Get.theme.textTheme.bodyLarge?.copyWith(
          fontSize: 18.sp,
          fontWeight: FontWeight.w500,
        ),
      ),
      hasLeadingIcon: false,
      actions: [_buildSearchButton(), _buildFilterButton()],
    );
  }

  Widget _buildSearchButton() {
    return IconButton(
      padding: EdgeInsets.zero,
      constraints: const BoxConstraints(),
      highlightColor: Colors.transparent,
      icon: Container(
        margin: EdgeInsets.only(left: 8.w),
        height: 40.h,
        width: 40.w,
        decoration: BoxDecoration(
          color: Get.theme.customColors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Get.theme.customColors.textfieldFillColor!),
        ),
        child: CustomImageView(
          imagePath: AssetConstants.icSearch,
          margin: const EdgeInsets.all(8.0),
        ),
      ),
      onPressed: controller.toggleSearchMode,
    );
  }

  Widget _buildFilterButton() {
    return IconButton(
      padding: EdgeInsets.zero,
      constraints: const BoxConstraints(),
      highlightColor: Colors.transparent,
      icon: Container(
        margin: EdgeInsets.only(left: 8.w),
        height: 40.h,
        width: 40.w,
        decoration: BoxDecoration(
          color: Get.theme.customColors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Get.theme.customColors.textfieldFillColor!),
        ),
        child: CustomImageView(
          imagePath: AssetConstants.icFilter,
          margin: const EdgeInsets.all(8.0),
        ),
      ),
      onPressed: () => _showFilterBottomSheet(Get.context!),
    );
  }

  FloatingActionButton _buildScannerFAB() {
    return FloatingActionButton(
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(100.0)),
      ),
      heroTag: 'business_card_scanner',
      onPressed: _openBarcodeScanner,
      backgroundColor: Get.theme.customColors.primaryColor,
      child: CustomImageView(
        imagePath: AssetConstants.icQrCode2,
        color: Get.theme.customColors.white,
      ),
    );
  }

  Future<void> _openBarcodeScanner() async {
    // Reset scanned data before opening scanner
    controller.scannedData.value = '';

    await Get.to(
      AiBarcodeScanner(
        hideSheetDragHandler: true,
        hideGalleryIcon: false,
        hideSheetTitle: true,
        onDispose: () {
          Logger.log("Barcode scanner disposed!");
        },
        hideGalleryButton: false,
        controller: MobileScannerController(
          detectionSpeed: DetectionSpeed.noDuplicates,
        ),
        onDetect: (BarcodeCapture capture) {
          if (capture.barcodes.isNotEmpty) {
            controller.scannedData.value =
                capture.barcodes.first.rawValue.toString();
            NavigationService.navigateBack();
          }
        },
        validator: (value) {
          return value.barcodes.isNotEmpty &&
              (value.barcodes.first.rawValue?.contains('flutter.dev') ?? false);
        },
      ),
    );

    // Only show dialog if QR was successfully scanned
    if (controller.scannedData.value.isNotEmpty) {
      Get.dialog(
        GroupSelectionDialog(
          scannedData: controller.scannedData.value,
          controller: controller,
        ),
        barrierDismissible: false,
      );
    }
  }

  Widget _buildBody(BuildContext context) {
    return Stack(children: [_buildBusinessCardList(), _buildSearchBar()]);
  }

  Widget _buildBusinessCardList() {
    return Obx(() {
      final hasData =
          controller.businessCardList.value?.data.isNotEmpty ?? false;
      if ((!controller.hasInitialData.value ||
              controller.isLoadingBusinessCardList.value) &&
          !hasData) {
        return BusinessCardShimmer(controller.isSearchActive.value);
      }

      if (hasData) {
        return _buildBusinessCardListContent();
      }

      return RefreshIndicator(
        onRefresh: () => controller.checkNetworkAndLoad(),
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: SizedBox(
            height: Get.height * 0.7,
            child: Center(
              child:
                  controller.isConnected.value
                      ? NoDataWidget(
                        message: AppStrings.T.lbl_no_business_card_found,
                        padding: EdgeInsets.zero,
                      )
                      : NoInternetWidget(
                        message: AppStrings.T.no_internet_connection,
                      ),
            ),
          ),
        ),
      );
    });
  }

  Widget _buildBusinessCardListContent() {
    return RefreshIndicator(
      onRefresh: () {
        if (controller.isConnected.value) {
          controller.businessCardList.value = null;
          if (controller.selectedGroupId.value == -1) {
            return controller.getBusinessCardList();
          } else {
            return controller.getFilteredBusinessCardList();
          }
        } else {
          return Future.value();
        }
      },
      child: Padding(
        padding: EdgeInsets.only(
          top: controller.isSearchActive.value ? 78.h : 8.h,
        ),
        child: _buildGroupedBusinessCardList(
          _getFilteredBusinessCards(
            controller.businessCardList.value?.data ?? [],
          ),
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Obx(
      () =>
          controller.isSearchActive.value
              ? Positioned(
                top: 10,
                left: 0,
                right: 0,
                child: Container(
                  color: Get.theme.scaffoldBackgroundColor,
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: TextInputField(
                    controller: controller.searchController,
                    focusNode: controller.searchFocusNode,
                    label: AppStrings.T.lbl_search_business_cards,
                    onChanged: (value) {
                      controller.searchText.value = value;
                    },
                    type: InputType.text,
                    textInputAction: TextInputAction.done,
                    suffixIcon:
                        controller.searchText.value.isNotEmpty
                            ? IconButton(
                              icon: Icon(
                                Icons.close,
                                color: Get.theme.customColors.greyTextColor,
                              ),
                              onPressed: _clearSearch,
                            )
                            : const SizedBox.shrink(),
                  ),
                ),
              )
              : const SizedBox.shrink(),
    );
  }

  void _clearSearch() {
    controller.searchController.clear();
    controller.searchText.value = '';
    controller.toggleSearchMode();
  }

  List<BusinessCardData> _getFilteredBusinessCards(
    List<BusinessCardData> allCards,
  ) {
    if (!controller.isSearchActive.value) {
      return allCards.where(_isInValidGroup).toList();
    }

    final searchTerm = controller.searchText.value.trim().toLowerCase();
    return allCards
        .where(
          (card) =>
              (_isInValidGroup(card) &&
                  ((card.name?.toLowerCase().contains(searchTerm) ?? false) ||
                      (card.occupation?.toLowerCase().contains(searchTerm) ??
                          false) ||
                      (card.groupName?.toLowerCase().contains(searchTerm) ??
                          false))),
        )
        .toList();
  }

  bool _isInValidGroup(BusinessCardData card) {
    return controller.groupList.value?.data.any(
          (group) => group.name?.toLowerCase() == card.groupName?.toLowerCase(),
        ) ??
        false;
  }

  Widget _buildGroupedBusinessCardList(List<BusinessCardData> cards) {
    // Group cards by their group name
    final Map<String, List<BusinessCardData>> groupedCards = {};

    for (final card in cards) {
      final groupName = card.groupName ?? 'Uncategorized';
      groupedCards.putIfAbsent(groupName, () => []).add(card);
    }

    // Sort groups alphabetically with case-insensitive comparison
    final sortedGroupNames =
        groupedCards.keys.toList()
          ..sort((a, b) => a.toLowerCase().compareTo(b.toLowerCase()));

    return ListView.builder(
      padding: EdgeInsets.only(bottom: 120.h),
      itemCount: groupedCards.isEmpty ? 1 : sortedGroupNames.length,
      itemBuilder: (context, index) {
        if (groupedCards.isEmpty) {
          return _buildEmptyResultsWidget();
        }

        final groupName = sortedGroupNames[index];
        final groupCards = groupedCards[groupName]!;

        return BusinessCardGroup(
          groupName: groupName,
          cards: groupCards,
          isLastGroup: index == sortedGroupNames.length - 1,
        );
      },
    );
  }

  Widget _buildEmptyResultsWidget() {
    final message =
        controller.isSearchActive.value &&
                controller.searchText.value.isNotEmpty
            ? "${AppStrings.T.lbl_no_results_found_for} '${controller.searchText.value}'"
            : AppStrings.T.lbl_no_business_card_found;
    return NoDataWidget(message: message);
  }

  void _showFilterBottomSheet(BuildContext context) {
    Get.bottomSheet(
      GroupFilterBottomSheet(controller: controller),
      isScrollControlled: true,
    );
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {
    controller.searchController.clear();
    controller.isSearchActive.value = false;
    controller.searchText.value = '';
    controller.searchFocusNode.unfocus();
  }
}
